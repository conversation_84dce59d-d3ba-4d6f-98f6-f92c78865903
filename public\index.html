<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title data-i18n="page_title">自然語言點餐系統</title>
      <!-- 外部資源 -->    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/theme/material.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/theme/monokai.min.css">    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/default.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="css/toast.css?v=1">
    <link rel="stylesheet" href="css/toast-fix.css?v=1">
    <link rel="stylesheet" href="css/language-switcher.css?v=1">
    <!-- 外部腳本 --><script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>    <script src="js/session-manager.js?v=1"></script>
    <script src="js/gemini-helper.js?v=1"></script>
    <script src="js/menu-price-validator.js?v=1"></script>
    <script src="js/enhanced-gemini-extraction.js?v=1"></script>
    <script src="js/checkout-fix.js?v=1"></script>
    <script src="js/order-confirmation-fix.js?v=1"></script>
    <script src="js/language-resources.js?v=1"></script>    <!-- 移除診斷腳本: order-summary-diagnostics.js -->
    <style>        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        
        /* 通知樣式 */
        #success-message, #error-message {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            min-width: 280px;
            transition: opacity 0.5s ease-in-out;
        }
        
        #success-message {
            background-color: #e8f5e9;
            border-left: 5px solid #2E7D32;
            color: #2E7D32;
        }
        
        #error-message {
            background-color: #ffebee;
            border-left: 5px solid #C62828;
            color: #C62828;
        }
        
        #success-message strong, #error-message strong {
            margin-right: 8px;
            font-weight: bold;
        }
        
        #success-message i, #error-message i {
            margin-right: 8px;
        }
        
        /* 選擇檔案按鈕樣式 */
        .file-select-btn {
            background-color: #2196F3;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            display: inline-block;
        }
        
        .file-select-btn:hover {
            background-color: #0b7dda;
        }
        
        /* USI AIOS 生成進度指示器樣式 */
        .generating-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: #e8f5e9;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .spinner {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            border: 3px solid #4CAF50;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        .generating-text {
            color: #2E7D32;
            font-size: 16px;
            margin: 0;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        /* USI AIOS 生成進度指示器樣式 */
        .generating-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: #e8f5e9;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .spinner {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            border: 3px solid #4CAF50;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        .generating-indicator p {
            color: #2e7d32;
            margin: 0;
            font-size: 16px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #4CAF50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        header h1 {
            margin: 0;
            font-size: 2em;
        }        .tabs {
            display: flex;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-wrap: wrap;
        }
        .tab {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            border-bottom-color: #4CAF50;
            color: #4CAF50;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        .file-drop-area {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .file-drop-area:hover {
            border-color: #4CAF50;
        }
        .btn {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        .alert-success {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }
        .alert-danger {
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
        }
        .alert ul {
            margin: 10px 0 0 20px;
            padding: 0;
        }
        .alert h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .format-hint {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 3px solid #4CAF50;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }
        
        .loading .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin: 10px auto;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading p {
            margin: 10px 0;
            color: #666;
            font-size: 14px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .preview-table th, .preview-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .preview-table th {
            background-color: #f5f5f5;
        }        .preview-container {
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .loading img {
            width: 50px;
            height: 50px;
        }
        /* 編輯器相關樣式 */
        .CodeMirror {
            height: auto;
            min-height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        .CodeMirror-focused {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        .app-prompt-result {
            margin: 15px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }        .app-prompt-result h4 {
            margin: 15px 0 10px;
            color: #333;
            font-size: 16px;
        }        .app-prompt-result pre {
            margin: 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        /* 語音識別相關樣式 */
        .input-with-mic {
            display: flex;
            position: relative;
        }
        
        .input-with-mic .form-control {
            flex: 1;
        }
        
        .btn-icon {
            margin-left: 10px;
            width: 42px;
            height: 42px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #4CAF50;
        }
        
        .btn-icon.recording {
            background-color: #f44336;
            animation: pulse 1.5s infinite;
        }
        
        .speech-status {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .speech-status.error {
            color: #f44336;
        }
        
        .speech-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }
        
        .speech-result:not(:empty) {
            display: block;
        }
        
        .interim-result {
            color: #999;
            font-style: italic;
        }
        
        .final-result {
            color: #333;
            font-weight: bold;
        }
        
        .order-result-container {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: none;
        }
        
        .order-result-container:not(:empty) {
            display: block;
        }
        
        .disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
          /* 程式碼高亮樣式 */
        pre code.hljs {
            padding: 1em;
            border-radius: 4px;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            tab-size: 4;
        }
        
        /* 覆蓋highlight.js的默認顏色，將所有元素設為白色 */
        .hljs-string, .hljs-title, .hljs-name, .hljs-type, .hljs-attribute, .hljs-symbol, .hljs-bullet,
        .hljs-addition, .hljs-variable, .hljs-template-tag, .hljs-template-variable, .hljs-literal,
        .hljs-number, .hljs-keyword, .hljs-selector-tag, .hljs-tag, .hljs-attr, .hljs-quote,
        .hljs-built_in, .hljs-builtin-name, .hljs-section, .hljs-selector-class, .hljs-selector-id {
            color: #ffffff !important;
        }

        /* 編輯器按鈕區域樣式 */
        .editor-buttons {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .editor-buttons .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .editor-buttons .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .editor-buttons .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>    <!-- 避免重複引入，codemirror.min.js已在頂部引入 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.12/mode/javascript/javascript.min.js"></script>
      <header>
        <h1 data-i18n="page_title">自然語言點餐系統</h1>
        <p data-i18n="page_subtitle">Architecture Agent 智慧零售應用實作</p>
        <!-- 語言切換選單將由JavaScript動態添加到這裡 -->
    </header>

    <!-- 會話狀態顯示 -->
    <div id="session-status" class="session-status" style="display: none; margin: 10px auto; max-width: 1200px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 15px;">
        <div class="session-info" style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
            <span class="session-id" style="font-weight: bold; color: #495057;">會話ID: <span id="current-session-id" style="font-family: monospace; color: #007bff;">-</span></span>
            <div class="session-indicators" style="display: flex; gap: 20px; flex-wrap: wrap;">
                <span id="menu-indicator" class="indicator" style="display: flex; align-items: center; gap: 5px;">📋 菜單: <span class="status" style="font-weight: bold; color: #dc3545;">未載入</span></span>
                <span id="prompt-indicator" class="indicator" style="display: flex; align-items: center; gap: 5px;">🤖 APPprompt: <span class="status" style="font-weight: bold; color: #dc3545;">未生成</span></span>
                <span id="language-indicator" class="indicator" style="display: flex; align-items: center; gap: 5px;">🌐 語言: <span class="status" style="font-weight: bold; color: #28a745;">zh-TW</span></span>
            </div>
            <button id="clear-session-btn" class="btn" style="background-color: #6c757d; color: white; padding: 5px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">清除會話</button>
        </div>
    </div>

    <div class="container"><div class="tabs">
            <div class="tab active" data-tab="menu-management" data-i18n="tab_menu_management">菜單管理</div>
            <div class="tab" data-tab="bdd-editor" data-i18n="tab_bdd_editor">BDD 編輯器</div>
            <!-- AAprompt 編輯器和 APPprompt 生成器在主頁面中隱藏 -->
            <!-- <div class="tab" data-tab="aa-prompt-editor" data-i18n="tab_aa_prompt_editor">AAprompt 編輯器</div> -->
            <!-- <div class="tab" data-tab="app-prompt-generator" data-i18n="tab_app_prompt_generator">APPprompt 生成器</div> -->
            <div class="tab" data-tab="natural-order" data-i18n="tab_natural_order">自然語言點餐</div>
        </div>        <!-- 移除全局結果容器，將其移動到相應頁籤內 -->

        <div class="tab-content active" id="menu-management">
            <h2 data-i18n="menu_management_title">菜單上傳與管理</h2>
              <div class="alert alert-success" id="success-message" style="display:none; position:fixed; top:20px; right:20px; z-index:9999; padding:15px; border-left:5px solid #2E7D32; box-shadow:0 4px 8px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-check-circle"></i> <span data-i18n="success_title">成功！</span></strong> <span id="success-content"></span>
            </div>
            <div class="alert alert-danger" id="error-message" style="display:none; position:fixed; top:20px; right:20px; z-index:9999; padding:15px; border-left:5px solid #C62828; box-shadow:0 4px 8px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-exclamation-circle"></i> <span data-i18n="error_title">錯誤！</span></strong> <span id="error-content"></span>
            </div>
            
            <div class="form-group">
                <label for="restaurant-id" data-i18n="restaurant_id_label">餐廳 ID</label>
                <input type="text" id="restaurant-id" class="form-control" data-i18n-placeholder="restaurant_id_placeholder" placeholder="請輸入餐廳識別碼" value="mcdonalds">
            </div>            <div class="file-drop-area" id="drop-area">
                <p data-i18n="drop_file_text">拖曳檔案到此處或點擊選擇檔案</p>
                <p class="small" data-i18n="supported_formats">支援的檔案格式：CSV, Excel, JSON</p>
                <input type="file" id="file-input" style="position: absolute; opacity: 0; height: 100%; width: 100%; top: 0; left: 0; cursor: pointer; z-index: 2;" accept=".csv,.xlsx,.json" data-i18n-title="select_file_tooltip">
                <button type="button" id="browse-btn" class="btn file-select-btn" data-i18n="select_file_btn">選擇檔案</button>
            </div>
            <style>
                .file-drop-area {
                    border: 2px dashed #ddd;
                    border-radius: 5px;
                    padding: 20px;
                    text-align: center;
                    margin-bottom: 20px;
                    position: relative;
                }
                
                .file-select-btn {
                    background-color: #4CAF50;
                    color: white;
                    padding: 8px 15px;
                    border-radius: 4px;
                    border: none;
                    cursor: pointer;
                    font-size: 14px;
                    margin-top: 10px;
                    display: inline-block;
                }
            </style>
            
            <div class="form-group">
                <label><input type="checkbox" id="auto-detect-category" checked> <span data-i18n="auto_detect_category">自動偵測分類</span></label>
            </div>
            
            <button id="upload-btn" class="btn" data-i18n="upload_menu_btn">上傳菜單</button>
              <div class="loading" id="loading">
                <p data-i18n="processing_text">處理中，請稍候...</p>
                <div class="spinner"></div>
            </div>
            
            <div class="preview-container" id="preview-container"></div>
        </div>        <div class="tab-content" id="bdd-editor">
            <h2 data-i18n="bdd_editor_title">BDD 編輯器</h2>
            <div class="alert alert-success" id="bdd-success-message" style="display:none; margin-bottom:15px; padding:15px; border-left:5px solid #2E7D32; box-shadow:0 2px 5px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-check-circle"></i> <span data-i18n="success_title">成功！</span></strong> <span id="bdd-success-content"></span>
            </div>
            <div class="alert alert-danger" id="bdd-error-message" style="display:none; margin-bottom:15px; padding:15px; border-left:5px solid #C62828; box-shadow:0 2px 5px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-exclamation-circle"></i> <span data-i18n="error_title">錯誤！</span></strong> <span id="bdd-error-content"></span>
            </div>            <div class="form-group">
                <label for="bdd-text" data-i18n="bdd_text_label">行為驅動規範 (BDD)</label>
                <textarea id="bdd-text" class="form-control" rows="15" placeholder="例如：&#10;Feature: 自然語言點餐&#10;  As a customer&#10;  I want to order food using natural language&#10;  So that I can place my order quickly and intuitively&#10;&#10;Scenario: 顧客使用文字輸入點餐..."></textarea>
            </div>            <div class="editor-buttons">
                <button id="save-bdd" class="btn" data-i18n="save_bdd_btn">儲存</button>
                <button id="refresh-bdd-language" class="btn" style="background-color: #FF9800; margin-left: 10px;" data-i18n="refresh_language_content" title="強制更新為當前語系的預設內容">🔄 刷新語言內容</button>
            </div>
        </div><div class="tab-content" id="aa-prompt-editor">
            <h2 data-i18n="aa_editor_title">AAprompt 編輯器</h2>

            <div class="alert alert-success" id="aa-success-message" style="display:none; margin-bottom:15px; padding:15px; border-left:5px solid #2E7D32; box-shadow:0 2px 5px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-check-circle"></i> <span data-i18n="success_title">成功！</span></strong> <span id="aa-success-content"></span>
            </div>
            <div class="alert alert-danger" id="aa-error-message" style="display:none; margin-bottom:15px; padding:15px; border-left:5px solid #C62828; box-shadow:0 2px 5px rgba(0,0,0,0.1);">
                <strong><i class="fas fa-exclamation-circle"></i> <span data-i18n="error_title">錯誤！</span></strong> <span id="aa-error-content"></span>
            </div>            <div class="form-group">
                <label for="aa-text" data-i18n="aa_text_label">Actor-Action 提示詞</label>
                <textarea id="aa-text" class="form-control" rows="15" placeholder="格式: 作為[角色]，[動作]，在[情境]的情況下，[限制條件]&#10;&#10;例如：&#10;作為自然語言點餐系統，解析顧客的點餐請求並識別菜單項目，在快速服務餐廳的情況下，確保準確識別菜單項目和數量，處理特殊要求和修改請求"></textarea>
            </div>              <div class="editor-buttons">
                <button id="save-aaprompt" class="btn" data-i18n="save_aaprompt_btn">儲存</button>
                <button id="refresh-aaprompt-language" class="btn" style="background-color: #FF9800; margin-left: 10px;" data-i18n="refresh_language_content" title="強制更新為當前語系的預設內容">🔄 刷新語言內容</button>
            </div>
        </div>

        <div class="tab-content" id="app-prompt-generator">            <h2 data-i18n="app_generator_title">APPprompt 生成器</h2>

            <div class="alert alert-success" id="generate-success"></div>
            <div class="alert alert-danger" id="generate-error"></div>

            <button id="generate-btn" class="btn" data-i18n="app_generator_btn">生成 APPprompt</button>
            <button id="download-btn" class="btn" style="background-color: #2196F3; margin-left: 10px; display: none;" data-i18n="download_appprompt_btn">下載完整APPprompt</button>

            <div class="preview-container" id="appprompt-container"></div>
        </div>        <div class="tab-content" id="natural-order">
            <h2 data-i18n="natural_order_title">快速點餐系統</h2>

            <!-- APPprompt 狀態提示區域 -->
            <div id="appprompt-status-indicator" class="alert" style="margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                <div id="appprompt-status-icon" style="font-size: 20px;">⏳</div>
                <div>
                    <strong id="appprompt-status-title" data-i18n="appprompt_not_ready">APPprompt尚未準備就緒</strong>
                    <div id="appprompt-status-message" style="font-size: 14px; margin-top: 5px;">正在檢查系統狀態...</div>
                </div>
            </div>

              <div class="alert alert-success" id="order-success" style="display:none;"></div>
            <div class="alert alert-danger" id="order-error" style="display:none;"></div>
              <div class="row" style="margin-bottom: 20px;">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="restaurant-selector" data-i18n="restaurant_selector_label">選擇餐廳</label>
                        <select id="restaurant-selector" class="form-control">
                            <option value="mcdonalds">McDonald's</option>
                            <option value="kfc">KFC</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="order-input" data-i18n="order_input_label">請輸入您的餐點需求</label>
                        <div class="input-with-mic">
                            <input type="text" id="order-input" class="form-control">
                            <button id="mic-button" class="btn btn-icon speech-feature" title="點擊開始語音點餐">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>
                        <div id="speech-status" class="speech-status"></div>
                        <div id="speech-result" class="speech-result" style="display:none;"></div>
                    </div>
                </div>
            </div>
            <!-- 嚴格模式選項已隱藏 -->
            <input type="checkbox" class="form-check-input" id="strict-mode-checkbox" style="display: none;">
            
            <div style="margin-top: 20px;">
                <button id="process-order-btn" class="btn" data-i18n="submit_order_btn">處理訂單</button>
                <button id="speech-toggle-btn" class="btn" onclick="toggleSpeech()" style="margin-left: 10px; background-color: #2196F3;" title="切換語音功能">
                    🔊 <span data-i18n="enable_speech">啟用語音</span>
                </button>
                <!-- 測試語音按鈕已隱藏 - 目前未使用
                <button id="speech-test-btn" class="btn" onclick="testSpeech()" style="margin-left: 10px; background-color: #FF9800; display: none;" title="測試語音功能">
                    🎵 <span data-i18n="test_speech">測試語音</span>
                </button>
                <button id="speech-diagnose-btn" class="btn" onclick="diagnoseSpeech()" style="margin-left: 10px; background-color: #9C27B0; display: none;" title="診斷語音系統">
                    🔍 <span data-i18n="diagnose_speech">診斷語音</span>
                </button>
                -->
            </div>
            
            <div class="order-result-container" id="order-result-container"></div>
            <div id="order-confirmation-modal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:10000; align-items:center; justify-content:center;">
                <div style="background-color:#fff; padding:30px; border-radius:8px; box-shadow:0 5px 15px rgba(0,0,0,0.3); text-align:center; max-width:400px; width:100%; overflow-y:auto; max-height:90vh;">
                    <h2 style="color:#4CAF50; margin-top:0;"><i class="fas fa-check-circle"></i> <span data-i18n="order_complete_title">訂單完成!</span></h2>
                    <p data-i18n="order_complete_message">感謝您的訂購！您的餐點正在準備中。</p>
                    <div style="margin-top:20px;">
                        <button onclick="closeOrderConfirmationModal()" class="btn" style="background-color:#f44336; margin-right:10px;" data-i18n="close_btn">關閉</button>
                        <button onclick="startNewOrder()" class="btn" style="background-color:#4CAF50;" data-i18n="new_order_btn">點新的ㄧ單</button>
                    </div>
                    <!-- Order Summary 和 Total Amount 已隱藏 -->
                    <div id="modal-order-summary" style="display:none;">
                        <!-- 訂單項目將在此處動態生成 -->
                    </div>
                    <div id="modal-total-amount" style="display:none;"></div>
                </div>
            </div>
        </div>
    </div>    <script>
        // 頁面加載完成後初始化所有功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化上傳功能
            setupUploadButton();

            // 設置儲存按鈕功能
            setupSaveButtons();

            // 設置選擇檔案按鈕
            setupBrowseButton();

            // 初始化會話狀態顯示
            initSessionStatus();
        });
          // Tab 切換功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有 active 狀態
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // 只有在離開APPprompt生成器標籤頁時才隱藏下載按鈕，但不清理內容
                const downloadBtn = document.getElementById('download-btn');
                if (downloadBtn && this.dataset.tab !== 'app-prompt-generator') {
                    downloadBtn.style.display = 'none';
                }

                // 設置當前為 active
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');

                // 如果切換到APPprompt生成器標籤頁，且有內容，則顯示下載按鈕
                if (this.dataset.tab === 'app-prompt-generator') {
                    const container = document.getElementById('appprompt-container');
                    if (container && container.innerHTML.trim() && downloadBtn) {
                        downloadBtn.style.display = 'inline-block';
                    }
                }

                // 如果切換到自然語言點餐頁籤，更新狀態提示並自動檢查生成APPprompt
                if (this.dataset.tab === 'natural-order') {
                    // 立即更新狀態提示
                    setTimeout(updateAPPPromptStatusIndicator, 100);
                    // 自動檢查並生成APPprompt
                    autoGenerateAPPPromptForOrdering();
                }
            });
        });
        
        // 注意：不再在這裡設置檔案上傳區域互動，而是交由 editor.js 處理
        // 這樣可以避免重複綁定事件處理器

        // 自動生成APPprompt用於點餐功能
        async function autoGenerateAPPPromptForOrdering() {
            try {
                console.log('檢查是否需要自動生成APPprompt...');

                // 檢查會話管理器是否可用
                if (!window.sessionManager) {
                    console.warn('會話管理器未初始化，無法自動生成APPprompt');
                    return;
                }

                // 首先檢查是否有菜單數據
                const status = window.sessionManager.getSessionStatus();
                if (!status.hasMenuData) {
                    console.log('尚未上傳菜單，無法生成APPprompt');
                    showToastMessage(getTranslation('please_upload_menu_first'), 'info');
                    return;
                }

                // 檢查是否已有APPprompt
                if (status.hasAppPrompt) {
                    console.log('APPprompt已存在，檢查是否需要重新生成...');

                    // 檢查APPprompt是否與當前菜單匹配
                    const currentAppPrompt = window.sessionManager.getAppPrompt();
                    const currentMenuData = window.sessionManager.getMenuData();

                    if (currentAppPrompt && currentMenuData) {
                        try {
                            const appPromptData = JSON.parse(currentAppPrompt);
                            const appPromptMenuData = appPromptData.parameters?.menu || appPromptData.menuData?.categories;

                            // 簡單比較菜單項目數量來判斷是否需要重新生成
                            const currentItemCount = currentMenuData.categories.reduce((sum, cat) => sum + cat.items.length, 0);
                            const appPromptItemCount = appPromptMenuData ?
                                (Array.isArray(appPromptMenuData) ?
                                    appPromptMenuData.reduce((sum, cat) => sum + (cat.items?.length || 0), 0) : 0) : 0;

                            if (currentItemCount !== appPromptItemCount) {
                                console.log(`菜單項目數量不匹配 (當前: ${currentItemCount}, APPprompt: ${appPromptItemCount})，需要重新生成`);
                                // 清除舊的APPprompt
                                window.sessionManager.setAppPrompt(null);
                            } else {
                                console.log('APPprompt與當前菜單匹配，無需重新生成');
                                return;
                            }
                        } catch (error) {
                            console.error('檢查APPprompt匹配性時出錯:', error);
                            // 出錯時重新生成
                            window.sessionManager.setAppPrompt(null);
                        }
                    } else {
                        console.log('APPprompt或菜單數據不完整，需要重新生成');
                        // 如果菜單數據不存在，不應該繼續生成
                        if (!currentMenuData) {
                            console.log('菜單數據不存在，無法生成APPprompt');
                            showToastMessage(getTranslation('please_upload_menu_first'), 'info');
                            return;
                        }
                    }
                }

                // 顯示生成中的提示
                showToastMessage(getTranslation('auto_generating_appprompt'), 'info');

                // 更新狀態提示為生成中
                const indicator = document.getElementById('appprompt-status-indicator');
                const icon = document.getElementById('appprompt-status-icon');
                const title = document.getElementById('appprompt-status-title');
                const message = document.getElementById('appprompt-status-message');

                if (indicator && icon && title && message) {
                    indicator.className = 'alert alert-info';
                    icon.textContent = '🔄';
                    title.textContent = getTranslation('auto_generating_appprompt');
                    message.textContent = '正在載入編輯器內容並生成APPprompt...';
                }

                // 確保編輯器已初始化
                await ensureEditorsInitialized();

                // 檢查編輯器是否有內容，如果沒有則載入預設內容
                if (window.bddEditor && !window.bddEditor.getValue().trim()) {
                    console.log('BDD編輯器為空，載入預設內容...');
                    await loadBDDDefaultContent();
                }

                if (window.aaPromptEditor && !window.aaPromptEditor.getValue().trim()) {
                    console.log('AAprompt編輯器為空，載入預設內容...');
                    await loadAAPromptDefaultContent();
                }

                // 等待一下確保內容載入完成
                await new Promise(resolve => setTimeout(resolve, 500));

                // 生成APPprompt
                console.log('開始自動生成APPprompt...');
                await generateAPPPrompt();

                console.log('APPprompt自動生成完成');
                showToastMessage(getTranslation('appprompt_auto_generated'), 'success');

                // 更新狀態提示為完成
                updateAPPPromptStatusIndicator();

            } catch (error) {
                console.error('自動生成APPprompt失敗:', error);
                showToastMessage(getTranslation('auto_generate_failed'), 'error');

                // 更新狀態提示為錯誤
                updateAPPPromptStatusIndicator();
            }
        }

        // 確保編輯器已初始化
        async function ensureEditorsInitialized() {
            let attempts = 0;
            const maxAttempts = 10;

            while ((!window.bddEditor || !window.aaPromptEditor) && attempts < maxAttempts) {
                console.log(`等待編輯器初始化... (嘗試 ${attempts + 1}/${maxAttempts})`);
                await new Promise(resolve => setTimeout(resolve, 200));
                attempts++;
            }

            if (!window.bddEditor || !window.aaPromptEditor) {
                throw new Error('編輯器初始化超時');
            }
        }

        // 更新APPprompt狀態提示
        function updateAPPPromptStatusIndicator() {
            const indicator = document.getElementById('appprompt-status-indicator');
            const icon = document.getElementById('appprompt-status-icon');
            const title = document.getElementById('appprompt-status-title');
            const message = document.getElementById('appprompt-status-message');

            if (!indicator || !icon || !title || !message) {
                return; // 如果元素不存在則跳過
            }

            if (!window.sessionManager) {
                // 會話管理器未初始化
                indicator.className = 'alert alert-warning';
                icon.textContent = '⚠️';
                title.textContent = getTranslation('appprompt_not_ready');
                message.textContent = getTranslation('system_initializing');
                return;
            }

            const status = window.sessionManager.getSessionStatus();

            if (!status.hasMenuData) {
                // 沒有菜單數據
                indicator.className = 'alert alert-info';
                icon.textContent = '📋';
                title.textContent = getTranslation('appprompt_not_ready');
                message.textContent = getTranslation('please_upload_menu_first');
            } else if (!status.hasAppPrompt) {
                // 有菜單但沒有APPprompt
                indicator.className = 'alert alert-warning';
                icon.textContent = '⏳';
                title.textContent = getTranslation('appprompt_not_ready');
                message.textContent = getTranslation('auto_generate_on_switch');
            } else {
                // APPprompt已準備就緒
                indicator.className = 'alert alert-success';
                icon.textContent = '✅';
                title.textContent = getTranslation('appprompt_ready');
                message.textContent = getTranslation('ready_to_order');
            }
        }

        // 會話狀態管理
        function initSessionStatus() {
            if (!window.sessionManager) {
                console.warn('會話管理器未初始化，延遲初始化會話狀態');
                setTimeout(initSessionStatus, 1000);
                return;
            }

            // 顯示會話狀態面板
            const sessionStatus = document.getElementById('session-status');
            if (sessionStatus) {
                sessionStatus.style.display = 'block';
            }

            // 更新會話狀態
            updateSessionStatus();

            // 設置清除會話按鈕
            const clearBtn = document.getElementById('clear-session-btn');
            if (clearBtn) {
                clearBtn.addEventListener('click', function() {
                    if (confirm(getTranslation('confirm_clear_session'))) {
                        window.sessionManager.clearSession();
                        updateSessionStatus();
                        showToastMessage(getTranslation('session_cleared'), 'success');
                    }
                });
            }

            // 定期更新會話狀態
            setInterval(updateSessionStatus, 5000);
        }

        function updateSessionStatus() {
            if (!window.sessionManager) return;

            const status = window.sessionManager.getSessionStatus();

            // 更新會話ID
            const sessionIdElement = document.getElementById('current-session-id');
            if (sessionIdElement) {
                sessionIdElement.textContent = status.sessionId.substring(0, 12) + '...';
            }

            // 更新菜單狀態
            const menuIndicator = document.getElementById('menu-indicator');
            if (menuIndicator) {
                const statusElement = menuIndicator.querySelector('.status');
                if (status.hasMenuData) {
                    statusElement.textContent = getTranslation('status_loaded');
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = getTranslation('status_not_loaded');
                    statusElement.style.color = '#dc3545';
                }
            }

            // 更新 APPprompt 狀態
            const promptIndicator = document.getElementById('prompt-indicator');
            if (promptIndicator) {
                const statusElement = promptIndicator.querySelector('.status');
                if (status.hasAppPrompt) {
                    statusElement.textContent = getTranslation('status_generated');
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = getTranslation('status_not_generated');
                    statusElement.style.color = '#dc3545';
                }
            }

            // 更新語言狀態
            const languageIndicator = document.getElementById('language-indicator');
            if (languageIndicator) {
                const statusElement = languageIndicator.querySelector('.status');
                statusElement.textContent = status.language;
                statusElement.style.color = '#28a745';
            }

            // 更新APPprompt狀態提示（如果在自然語言點餐頁籤）
            const naturalOrderTab = document.getElementById('natural-order');
            if (naturalOrderTab && naturalOrderTab.classList.contains('active')) {
                updateAPPPromptStatusIndicator();
            }
        }

        // 菜單上傳功能的事件處理
        function setupUploadButton() {
            // 確保只綁定一次事件處理
            const uploadBtn = document.getElementById('upload-btn');
            const fileInput = document.getElementById('file-input');
            const dropArea = document.getElementById('drop-area');
            
            // 檢查是否已經初始化過，避免重複設置
            if (window.uploadButtonInitialized) {
                console.log('上傳按鈕已經初始化過，跳過');
                return;
            }
            
            // 標記為已初始化
            window.uploadButtonInitialized = true;
            
            // 確保拖放區域和文件輸入框的點擊事件正確綁定
            if (dropArea && fileInput) {
                console.log('設置拖放區域點擊事件，觸發文件選擇對話框');
                  // 調試日誌
                console.log('不需要在這裡設置拖放區域點擊事件，由file-input覆蓋整個區域處理');
            }
            
            if (uploadBtn && !uploadBtn.dataset.initialized) {
                // 標記為已初始化
                uploadBtn.dataset.initialized = 'true';
                
                uploadBtn.addEventListener('click', async (event) => {
                    event.preventDefault(); // 防止表單提交
                    event.stopPropagation(); // 防止事件冒泡
                    
                    // 確保fileInput存在並且有files屬性
                    if (!fileInput || !fileInput.files) {
                        showError('檔案選擇元素不存在或無效');
                        console.error('檔案輸入元素不存在或無效:', fileInput);
                        return;
                    }
                    
                    const restaurantId = document.getElementById('restaurant-id').value.trim();
                    const autoDetectCategory = document.getElementById('auto-detect-category').checked;
                    
                    const successMessage = document.getElementById('success-message');
                    const errorMessage = document.getElementById('error-message');
                    const loading = document.getElementById('loading');
                    
                    console.log("上傳按鈕被點擊");
                    console.log("檔案:", fileInput.files);
                    console.log("餐廳ID:", restaurantId);
                    
                    // 防止重複點擊
                    uploadBtn.disabled = true;
                    
                    try {
                        if (!fileInput.files || fileInput.files.length === 0) {
                            throw new Error(getTranslation('please_select_file'));
                        }
                        
                        if (!restaurantId) {
                            throw new Error('請輸入餐廳 ID');
                        }
                        
                        const formData = new FormData();
                        formData.append('file', fileInput.files[0]);
                        formData.append('restaurant_id', restaurantId);
                        formData.append('auto_detect_category', String(autoDetectCategory));
                        
                        // 顯示載入中狀態
                        loading.innerHTML = `
                            <p>正在處理您的菜單檔案</p>
                            <p class="small">檔案：${fileInput.files[0].name}</p>
                            <p class="small">大小：${formatFileSize(fileInput.files[0].size)}</p>
                            <p class="small">餐廳 ID：${restaurantId}</p>
                        `;
                        loading.style.display = 'block';
                        errorMessage.style.display = 'none';
                        successMessage.style.display = 'none';
                        
                        console.log("開始上傳檔案...");
                        const response = await fetch('/api/menu/upload', {
                            method: 'POST',
                            body: formData
                        });
                        
                        console.log("收到伺服器回應:", response);
                        const result = await response.json();
                        console.log("回應數據:", result);
                        
                        loading.style.display = 'none';
                        uploadBtn.disabled = false;
                        
                        if (result.success) {
                            // 保存菜單數據到會話管理器
                            if (window.sessionManager && result.data) {
                                window.sessionManager.setMenuData(result.data);
                                console.log('菜單數據已保存到會話管理器');

                                // 更新會話狀態顯示
                                if (typeof updateSessionStatus === 'function') {
                                    updateSessionStatus();
                                }
                            }

                            // 顯示成功訊息
                            successMessage.innerHTML = `
                                <h4>${getTranslation('menu_upload_success')}</h4>
                                <ul>
                                    <li>${getTranslation('file_name')}：${result.details.fileName}</li>
                                    <li>${getTranslation('file_size')}：${formatFileSize(result.details.fileSize)}</li>
                                    <li>${getTranslation('restaurant_id')}：${result.details.restaurantId}</li>
                                    <li>${getTranslation('total_items')}：${result.details.itemCount} ${getTranslation('items_unit')}</li>
                                    <li>${getTranslation('category_info')}：
                                        <ul>
                                            ${result.details.categories.map(cat => {
                                                const currentLang = getCurrentLanguage();
                                                let categoryName;
                                                if (currentLang === 'zh-TW') {
                                                    categoryName = cat.name_zh || cat.name_en || cat.name_jp;
                                                } else if (currentLang === 'ja-JP') {
                                                    categoryName = cat.name_jp || cat.name_en || cat.name_zh;
                                                } else {
                                                    categoryName = cat.name_en || cat.name_zh || cat.name_jp;
                                                }
                                                return `<li>${categoryName}: ${cat.itemCount} ${getTranslation('items_unit')}</li>`;
                                            }).join('')}
                                        </ul>
                                    </li>
                                </ul>
                            `;
                            successMessage.style.display = 'block';
                            showMenuPreview(result.data);

                            // 清理文件選擇，以便下次可以再次選擇同一文件
                            fileInput.value = "";
                              // 顯示吐司消息
                            if (typeof showToastMessage === 'function') {
                                const successMsg = getTranslation('menu_upload_success');
                                const processedMsg = getTranslation('processed_items').replace('{count}', result.details.itemCount);
                                showToastMessage(`<strong>${successMsg}</strong><br>${processedMsg}`, 'success');
                            }
                        } else {
                            const errorMessages = result.errors || ['處理菜單時發生未知錯誤'];
                            showError(errorMessages);
                            
                            // 顯示錯誤吐司消息
                            if (typeof showToastMessage === 'function') {
                                const failedMsg = getTranslation('menu_upload_failed');
                                showToastMessage(`<strong>${failedMsg}</strong><br>${Array.isArray(errorMessages) ? errorMessages[0] : errorMessages}`, 'error');
                            }
                        }
                    } catch (error) {
                        loading.style.display = 'none';
                        uploadBtn.disabled = false;
                        const uploadErrorMsg = getTranslation('upload_error');
                        showError(`${uploadErrorMsg}：${error.message}`);
                        
                        // 顯示錯誤吐司消息
                        if (typeof showToastMessage === 'function') {
                            showToastMessage(`<strong>${uploadErrorMsg}</strong><br>${error.message}`, 'error');
                        }
                    }
                });
            }
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
        }

        function showError(message) {
            const errorMessage = document.getElementById('error-message');
            let errorHtml = `<h4>${getTranslation('menu_upload_failed')}</h4>`;
            
            if (Array.isArray(message)) {
                errorHtml += '<ul>';
                message.forEach(msg => {
                    errorHtml += `<li>${msg}</li>`;
                });
                errorHtml += '</ul>';
            } else {
                errorHtml += `<p>${message}</p>`;
            }

            if (message.toLowerCase().includes('格式') || message.toLowerCase().includes('type')) {
                const currentLang = getCurrentLanguage();
                let formatTitle, csvText, excelText, jsonText;
                
                if (currentLang === 'en-US') {
                    formatTitle = 'Supported File Formats:';
                    csvText = 'CSV files (.csv)';
                    excelText = 'Excel files (.xlsx)';
                    jsonText = 'JSON files (.json)';
                } else if (currentLang === 'ja-JP') {
                    formatTitle = 'サポートされているファイル形式:';
                    csvText = 'CSVファイル (.csv)';
                    excelText = 'Excelファイル (.xlsx)';
                    jsonText = 'JSONファイル (.json)';
                } else {
                    formatTitle = '支援的檔案格式：';
                    csvText = 'CSV 檔案 (.csv)';
                    excelText = 'Excel 檔案 (.xlsx)';
                    jsonText = 'JSON 檔案 (.json)';
                }
                
                errorHtml += `
                    <div class="format-hint">
                        <p><strong>${formatTitle}</strong></p>
                        <ul>
                            <li>${csvText}</li>
                            <li>${excelText}</li>
                            <li>${jsonText}</li>
                        </ul>
                    </div>`;
            }

            errorMessage.innerHTML = errorHtml;
            errorMessage.style.display = 'block';
        }

        function showMenuPreview(menuData) {
            const previewContainer = document.getElementById('preview-container');
            
            let html = `
                <h3>${getTranslation('menu_preview')}：${menuData.restaurant_name}</h3>
                <p>${getTranslation('version')}：${menuData.version}</p>
            `;
            
            menuData.categories.forEach(category => {
                const currentLang = getCurrentLanguage();
                let categoryName = category.name_zh;
                if (currentLang === 'en-US' && category.name_en) {
                    categoryName = category.name_en;
                } else if (currentLang === 'ja-JP' && category.name_jp) {
                    categoryName = category.name_jp;
                }
                
                html += `
                    <h4>${categoryName}</h4>
                    <table class="preview-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>${getTranslation('menu_item_name')}</th>
                                <th>${getTranslation('menu_item_price')}</th>
                                <th>${getTranslation('menu_item_category')}</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                category.items.forEach(item => {
                    let itemName = item.name_zh;
                    let itemPrice = item.price;
                    let currency = 'NT$';
                    
                    if (currentLang === 'en-US' && item.name_en) {
                        itemName = item.name_en;
                    } else if (currentLang === 'ja-JP') {
                        if (item.name_jp) itemName = item.name_jp;
                        if (item.price_jp) {
                            itemPrice = item.price_jp;
                            currency = '¥';
                        }
                    }
                    
                    // 根據當前語言顯示category名稱
                    let itemCategory = item.category || '-';
                    if (itemCategory !== '-') {
                        if (currentLang === 'en-US' && category.name_en) {
                            itemCategory = categoryName; // 使用已翻譯的category名稱
                        } else if (currentLang === 'ja-JP' && category.name_jp) {
                            itemCategory = categoryName; // 使用已翻譯的category名稱
                        }
                    }
                    
                    html += `
                        <tr>
                            <td>${item.id}</td>
                            <td>${itemName}</td>
                            <td>${currency}${itemPrice}</td>
                            <td>${itemCategory}</td>
                        </tr>
                    `;
                });
                
                html += `
                        </tbody>
                    </table>
                `;
            });
            
            previewContainer.innerHTML = html;
        }        
        
        // BDD、AAprompt 編輯器功能和 APPprompt 生成器功能已移至 editor.js        // 模擬功能已移至 editor.js
    </script>    
    <!-- 示例訂單處理腳本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 處理示例訂單點擊
            const exampleButtons = document.querySelectorAll('.example-order');
            exampleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const orderText = this.getAttribute('data-order');
                    const orderInput = document.getElementById('order-input');
                    if (orderInput && orderText) {
                        orderInput.value = orderText;
                    }
                });
            });
            
            // 初始化訂單系統
            if (typeof initOrderSystem === 'function') {
                console.log('初始化訂單系統');
                initOrderSystem();
            } else {
                console.error('訂單系統初始化函數未找到');
            }
        });
    </script>    <!-- 自定義編輯器功能 -->    <script src="js/editor.js"></script>    <!-- 語音識別功能 -->    <script src="js/speech-recognition.js"></script>
      <!-- USI AIOS 輔助功能已在頭部載入 -->    <!-- 菜單項目顯示修復 -->    <script src="js/menu-display-fixer.js"></script><!-- 結帳功能修復腳本已移至頭部載入 -->
      <!-- 檔案上傳修復腳本 -->
    <script src="js/fix-upload.js"></script>
      <!-- 表格數據同步 -->
    <script src="js/table-data-sync.js"></script>
  
      <!-- 訂單處理功能 -->
    <script src="js/order.js"></script>
      <!-- 訂單系統清理腳本 -->
    <script src="js/order-cleanup.js"></script>
    
    <!-- 語音合成功能 -->
    <script>
        // 語音合成功能
        const speechSynthesis = window.speechSynthesis;
        
        // 語音狀態管理
        let speechEnabled = true;
        let lastUserInteraction = Date.now();
        
        // 記錄用戶互動
        function recordUserInteraction() {
            lastUserInteraction = Date.now();
        }
        
        // 檢查語音權限
        function checkSpeechPermission() {
            // 檢查是否在用戶互動後的合理時間內
            const timeSinceInteraction = Date.now() - lastUserInteraction;
            return timeSinceInteraction < 5000; // 5秒內的互動被認為是有效的
        }
        
        // 增強的文本轉語音函數
        function speakText(text, forceSpeak = false) {
            if (!text || text.trim() === '') {
                console.log('語音文本為空，跳過播放');
                return;
            }

            if (!speechEnabled && !forceSpeak) {
                console.log('語音已被用戶禁用');
                return;
            }

            // 檢查瀏覽器是否支援語音合成
            if (!window.speechSynthesis) {
                console.warn('瀏覽器不支援語音合成功能');
                return;
            }

            // 記錄用戶互動
            recordUserInteraction();

            // 如果已有語音正在播放，先停止
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                // 等待一小段時間確保停止完成
                setTimeout(() => {
                    performSpeech(text);
                }, 100);
            } else {
                performSpeech(text);
            }
        }

        // 執行語音播放的核心函數
        function performSpeech(text) {
            try {
                const utterance = new SpeechSynthesisUtterance(text);

                // 根據當前語言設定調整語音語言
                const currentLanguage = getCurrentLanguage();
                if (currentLanguage === 'en-US') {
                    utterance.lang = 'en-US';
                } else if (currentLanguage === 'ja-JP') {
                    utterance.lang = 'ja-JP';
                } else {
                    utterance.lang = 'zh-TW';
                }

                utterance.rate = 0.9; // 稍微慢一點，更清楚
                utterance.pitch = 1.0;
                utterance.volume = 0.8;

                // 添加成功和錯誤處理
                utterance.onstart = function() {
                    console.log('✅ 語音開始播放:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
                };

                utterance.onend = function() {
                    console.log('✅ 語音播放完成');
                };

                utterance.onerror = function(event) {
                    console.warn('❌ 語音播放失敗:', event.error);

                    // 根據錯誤類型處理
                    if (event.error === 'not-allowed') {
                        console.log('語音權限被拒絕，顯示權限提示');
                        showSpeechPermissionPrompt();
                    } else if (event.error === 'network') {
                        console.log('網路錯誤，嘗試重試');
                        setTimeout(() => {
                            retrySpeech(text, 1);
                        }, 1000);
                    } else {
                        console.log('其他語音錯誤:', event.error);
                    }
                };

                // 等待語音引擎準備就緒
                if (speechSynthesis.getVoices().length === 0) {
                    speechSynthesis.addEventListener('voiceschanged', function() {
                        speechSynthesis.speak(utterance);
                    }, { once: true });
                } else {
                    speechSynthesis.speak(utterance);
                }

            } catch (error) {
                console.warn('❌ 語音播放異常:', error);

                // 檢查權限並顯示提示
                if (!checkSpeechPermission()) {
                    showSpeechPermissionPrompt();
                } else {
                    // 嘗試重試
                    setTimeout(() => {
                        retrySpeech(text, 1);
                    }, 1000);
                }
            }
        }

        // 語音重試函數
        function retrySpeech(text, retryCount) {
            if (retryCount > 3) {
                console.warn('語音重試次數超過限制，放棄播放');
                return;
            }

            console.log(`嘗試重試語音播放 (第${retryCount}次):`, text.substring(0, 30));

            try {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-TW';
                utterance.rate = 0.9;

                utterance.onerror = function(event) {
                    console.warn(`重試${retryCount}次失敗:`, event.error);
                    if (retryCount < 3) {
                        setTimeout(() => {
                            retrySpeech(text, retryCount + 1);
                        }, 2000);
                    }
                };

                speechSynthesis.speak(utterance);
            } catch (error) {
                console.warn(`重試${retryCount}次異常:`, error);
                if (retryCount < 3) {
                    setTimeout(() => {
                        retrySpeech(text, retryCount + 1);
                    }, 2000);
                }
            }
        }
        
        // 顯示語音權限提示
        function showSpeechPermissionPrompt() {
            const promptDiv = document.createElement('div');
            promptDiv.id = 'speech-permission-prompt';
            promptDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
            `;
            
            promptDiv.innerHTML = `
                <div style="margin-bottom: 10px;">
                    🔊 ${getTranslation('speech_permission_needed') || '需要語音權限'}
                </div>
                <button onclick="enableSpeechWithInteraction()" style="
                    background: white;
                    color: #4CAF50;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin-right: 8px;
                ">${getTranslation('enable_speech') || '啟用語音'}</button>
                <button onclick="closeSpeechPrompt()" style="
                    background: transparent;
                    color: white;
                    border: 1px solid white;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                ">${getTranslation('close') || '關閉'}</button>
            `;
            
            // 移除現有提示
            const existing = document.getElementById('speech-permission-prompt');
            if (existing) {
                existing.remove();
            }
            
            document.body.appendChild(promptDiv);
            
            // 5秒後自動關閉
            setTimeout(() => {
                if (document.getElementById('speech-permission-prompt')) {
                    promptDiv.remove();
                }
            }, 5000);
        }
        
        // 通過用戶互動啟用語音
        function enableSpeechWithInteraction() {
            recordUserInteraction();
            speechEnabled = true;
            
            // 測試語音播放
            const testText = getTranslation('speech_enabled') || '語音已啟用';
            speakText(testText, true);
            
            closeSpeechPrompt();
        }
        
        // 關閉語音提示
        function closeSpeechPrompt() {
            const prompt = document.getElementById('speech-permission-prompt');
            if (prompt) {
                prompt.remove();
            }
        }
        
        // 切換語音開關
        function toggleSpeech() {
            speechEnabled = !speechEnabled;
            recordUserInteraction();

            const message = speechEnabled ?
                (getTranslation('speech_enabled') || '語音已啟用') :
                (getTranslation('speech_disabled') || '語音已禁用');

            // 更新按鈕文字和樣式
            updateSpeechToggleButton();

            showToastMessage(message, speechEnabled ? 'success' : 'info');

            if (speechEnabled) {
                speakText(message, true);
            }
        }

        // 測試語音功能
        function testSpeech() {
            console.log('=== 語音測試開始 ===');

            // 檢查基本條件
            console.log('瀏覽器支援語音合成:', !!window.speechSynthesis);
            console.log('語音已啟用:', speechEnabled);
            console.log('可用語音列表:', speechSynthesis.getVoices().length);

            recordUserInteraction();

            // 強制啟用語音進行測試
            speechEnabled = true;
            updateSpeechToggleButton();

            const testMessage = getTranslation('speech_test_message') || '語音測試：您好，這是語音功能測試';
            console.log('🔊 開始語音測試:', testMessage);
            console.log('語音合成狀態 - speaking:', speechSynthesis.speaking, 'pending:', speechSynthesis.pending);

            // 直接使用基本的語音合成進行測試
            try {
                const utterance = new SpeechSynthesisUtterance(testMessage);
                utterance.lang = 'zh-TW';
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;

                utterance.onstart = function() {
                    console.log('✅ 測試語音開始播放');
                    showToastMessage('語音測試開始播放', 'success');
                };

                utterance.onend = function() {
                    console.log('✅ 測試語音播放完成');
                    showToastMessage('語音測試完成', 'success');
                };

                utterance.onerror = function(event) {
                    console.error('❌ 測試語音播放失敗:', event.error);
                    showToastMessage(`語音測試失敗: ${event.error}`, 'error');
                };

                console.log('正在調用 speechSynthesis.speak()...');
                speechSynthesis.speak(utterance);

                showToastMessage('語音測試已啟動，請等待播放', 'info');

            } catch (error) {
                console.error('❌ 語音測試異常:', error);
                showToastMessage(`語音測試異常: ${error.message}`, 'error');
            }

            console.log('=== 語音測試結束 ===');
        }

        // 強制啟用語音並測試
        function forceEnableSpeechAndTest() {
            recordUserInteraction();
            speechEnabled = true;
            updateSpeechToggleButton();

            const testMessage = getTranslation('speech_force_test') || '語音已強制啟用，正在測試語音功能';
            console.log('🔊 強制啟用語音並測試:', testMessage);
            speakText(testMessage, true);

            showToastMessage('語音已強制啟用並開始測試', 'success');
        }


        
        // 更新語音切換按鈕
        function updateSpeechToggleButton() {
            const speechToggleBtn = document.getElementById('speech-toggle-btn');
            if (speechToggleBtn) {
                const span = speechToggleBtn.querySelector('span');
                if (speechEnabled) {
                    speechToggleBtn.style.backgroundColor = '#4CAF50';
                    speechToggleBtn.innerHTML = '🔊 <span data-i18n="speech_enabled">' + (getTranslation('speech_enabled') || '語音已啟用') + '</span>';
                } else {
                    speechToggleBtn.style.backgroundColor = '#f44336';
                    speechToggleBtn.innerHTML = '🔇 <span data-i18n="enable_speech">' + (getTranslation('enable_speech') || '啟用語音') + '</span>';
                }
            }
        }

        // 診斷語音系統 - 移到全局作用域
        window.diagnoseSpeech = function() {
            console.log('=== 語音系統診斷開始 ===');

            const diagnosticInfo = {
                browserSupport: !!window.speechSynthesis,
                speechEnabled: speechEnabled,
                voicesCount: speechSynthesis ? speechSynthesis.getVoices().length : 0,
                speaking: speechSynthesis ? speechSynthesis.speaking : false,
                pending: speechSynthesis ? speechSynthesis.pending : false,
                paused: speechSynthesis ? speechSynthesis.paused : false,
                userAgent: navigator.userAgent,
                language: navigator.language,
                currentLanguage: getCurrentLanguage(),
                lastUserInteraction: Date.now() - lastUserInteraction,
                permissions: 'unknown'
            };

            console.log('語音系統診斷結果:', diagnosticInfo);

            // 檢查權限
            if (navigator.permissions) {
                navigator.permissions.query({name: 'microphone'}).then(function(result) {
                    diagnosticInfo.permissions = result.state;
                    console.log('麥克風權限狀態:', result.state);
                }).catch(function(error) {
                    console.log('無法檢查麥克風權限:', error);
                });
            }

            // 列出可用的語音
            if (speechSynthesis) {
                const voices = speechSynthesis.getVoices();
                console.log('可用語音列表:');
                voices.forEach((voice, index) => {
                    console.log(`${index + 1}. ${voice.name} (${voice.lang}) - ${voice.localService ? '本地' : '遠程'}`);
                });

                if (voices.length === 0) {
                    console.warn('⚠️ 沒有可用的語音，嘗試等待語音載入...');
                    speechSynthesis.addEventListener('voiceschanged', function() {
                        const newVoices = speechSynthesis.getVoices();
                        console.log('語音載入完成，可用語音數量:', newVoices.length);
                        newVoices.forEach((voice, index) => {
                            console.log(`${index + 1}. ${voice.name} (${voice.lang}) - ${voice.localService ? '本地' : '遠程'}`);
                        });
                    }, { once: true });
                }
            }

            // 顯示診斷結果
            let diagnosticMessage = `
                <h3>🔍 語音系統診斷結果</h3>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>瀏覽器支援: ${diagnosticInfo.browserSupport ? '✅ 是' : '❌ 否'}</li>
                    <li>語音已啟用: ${diagnosticInfo.speechEnabled ? '✅ 是' : '❌ 否'}</li>
                    <li>可用語音數量: ${diagnosticInfo.voicesCount}</li>
                    <li>當前狀態: ${diagnosticInfo.speaking ? '正在播放' : '空閒'}</li>
                    <li>瀏覽器: ${diagnosticInfo.userAgent.includes('Chrome') ? 'Chrome' : diagnosticInfo.userAgent.includes('Firefox') ? 'Firefox' : diagnosticInfo.userAgent.includes('Safari') ? 'Safari' : '其他'}</li>
                    <li>語言設定: ${diagnosticInfo.currentLanguage}</li>
                    <li>用戶互動: ${diagnosticInfo.lastUserInteraction < 5000 ? '✅ 最近' : '⚠️ 較久前'}</li>
                </ul>
                <p style="font-size: 12px; color: #666;">詳細信息請查看瀏覽器Console</p>
            `;

            showToastMessage(diagnosticMessage, 'info');

            console.log('=== 語音系統診斷結束 ===');
        };
    </script>
      <!-- 自然語言點餐功能 -->
    <script>
        // 增加項目數量的函數
        function increaseItemQuantity(itemId) {
            const item = document.getElementById(itemId);
            if (!item) {
                console.error('找不到項目:', itemId);
                return;
            }
            
            // 獲取當前數量並增加
            let quantity = parseInt(item.getAttribute('data-quantity')) || 1;
            quantity += 1;
            
            // 更新數量顯示和屬性
            item.setAttribute('data-quantity', quantity);
            const quantityElement = item.querySelector('.item-quantity');
            if (quantityElement) {
                quantityElement.textContent = quantity;
            }
            
            // 更新項目總價
            updateItemTotal(item);
            
            // 更新整體總價
            updateOrderTotal();
            
            // 顯示提示訊息
            const itemName = item.getAttribute('data-name');
            showToastMessage(`已增加 ${itemName} 的數量`, 'info');
        }

        // 減少項目數量的函數
        function decreaseItemQuantity(itemId) {
            const item = document.getElementById(itemId);
            if (!item) {
                console.error('找不到項目:', itemId);
                return;
            }
            
            // 獲取當前數量並減少
            let quantity = parseInt(item.getAttribute('data-quantity')) || 1;
            
            // 如果數量為1，則詢問是否要移除
            if (quantity <= 1) {
                if (confirm('確定要移除此項目嗎？')) {
                    removeItem(itemId);
                }
                return;
            }
            
            // 減少數量
            quantity -= 1;
            
            // 更新數量顯示和屬性
            item.setAttribute('data-quantity', quantity);
            const quantityElement = item.querySelector('.item-quantity');
            if (quantityElement) {
                quantityElement.textContent = quantity;
            }
            
            // 更新項目總價
            updateItemTotal(item);
            
            // 更新整體總價
            updateOrderTotal();
            
            // 顯示提示訊息
            const itemName = item.getAttribute('data-name');
            showToastMessage(`已減少 ${itemName} 的數量`, 'info');
        }

        // 移除項目的函數
        function removeItem(itemId) {
            const item = document.getElementById(itemId);
            if (!item) {
                console.error('找不到項目:', itemId);
                return;
            }
            
            // 獲取項目名稱用於提示訊息
            const itemName = item.getAttribute('data-name');
            
            // 從表格中移除項目
            item.remove();
            
            // 更新整體總價
            updateOrderTotal();
            
            // 顯示提示訊息
            showToastMessage(`已移除 ${itemName}`, 'info');
            
            // 檢查是否還有餐點項目
            const remainingItems = document.querySelectorAll('.preview-table tbody tr');
            if (remainingItems.length === 0) {
                // 如果沒有剩餘項目，顯示空訂單提示
                const orderResultContainer = document.getElementById('order-result-container');
                if (orderResultContainer) {
                    orderResultContainer.innerHTML = `
                        <div class="no-matches">
                            <h3>您的訂單已清空</h3>
                            <p>請重新輸入您的點餐需求</p>
                        </div>
                    `;
                }
            }
        }        // 更新單個項目的總價
        function updateItemTotal(item) {
            const price = parseFloat(item.getAttribute('data-price')) || 0;
            const quantity = parseInt(item.getAttribute('data-quantity')) || 1;
            const totalPrice = price * quantity;
            
            const totalElement = item.querySelector('.item-total');
            if (totalElement) {
                totalElement.textContent = `NT$${totalPrice}`;
                // 確保item擁有totalPrice屬性，用於後續計算
                item.setAttribute('data-total', totalPrice);
            }
        }        // 更新訂單總價
        function updateOrderTotal() {
            let total = 0;
            const rows = document.querySelectorAll('.preview-table tbody tr');
            
            rows.forEach(row => {
                const price = parseFloat(row.getAttribute('data-price')) || 0;
                const quantity = parseInt(row.getAttribute('data-quantity')) || 1;
                total += price * quantity;
            });
            
            // 更新總計顯示
            const totalElement = document.querySelector('.preview-table tfoot strong');
            if (totalElement) {
                totalElement.textContent = `NT$${total}`;
            }
            
            // 將計算的總價存儲為全域變數，供其他函數使用
            window.currentTotalAmount = total;
            console.log('訂單總金額已更新: NT$' + total);
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const processOrderBtn = document.getElementById('process-order-btn');
            if (processOrderBtn) {
            processOrderBtn.addEventListener('click', () => {
                recordUserInteraction();
                processOrder();
            });
        }
            
            // 初始化語音按鈕狀態
            updateSpeechToggleButton();
            
            const orderInput = document.getElementById('order-input');
            if (orderInput) {
                orderInput.addEventListener('keypress', function(event) {
                    if (event.key === 'Enter') {
                        processOrder();
                    }                });
            }
        });
        
        async function processOrder() {
            const orderInput = document.getElementById('order-input');
            const orderText = orderInput?.value?.trim();
            // 嚴格模式預設關閉
            const useStrictMode = false;
            const restaurantSelector = document.getElementById('restaurant-selector');
            const selectedRestaurant = restaurantSelector?.value || 'mcdonalds';

            if (!orderText) {
                showOrderError(getTranslation('please_enter_order_request'));
                return;
            }

            // 檢查會話管理器是否可用
            if (!window.sessionManager) {
                showOrderError(getTranslation('session_manager_not_initialized'));
                return;
            }

            // 檢查是否有可用的 APPprompt
            const appPrompt = window.sessionManager.getAppPrompt();
            if (!appPrompt) {
                showOrderError(getTranslation('no_appprompt_available'));
                return;
            }

            // 增強的處理中提示 - 使用新的 AI 視覺化
            showEnhancedOrderProcessing(getTranslation('ai_thinking_text'));

            try {
                console.log('🤖 正在使用會話管理器處理訂單:', orderText);
                console.log('🔗 會話ID:', window.sessionManager.sessionId);

                // 使用會話管理器處理點餐請求
                const result = await window.sessionManager.processOrder(orderText);

                console.log('🤖 USI AIOS 處理結果:', result);

                showOrderSuccess('✅ ' + getTranslation('gemini_analysis_success'));

                // 直接顯示 USI AIOS 的自然語言回應
                if (result) {
                    // result 包含 USI AIOS 的自然語言回應
                    showGeminiOrderResult(result, {});
                } else {
                    showOrderError(getTranslation('usi_aios_response_format_error'));
                }
            } catch (error) {
                console.error('❌ 呼叫 USI AIOS API 失敗:', error);
                showOrderError(`🚫 ${getTranslation('gemini_api_connection_failed')}: ${error.message}，${getTranslation('check_network_connection')}`);
            }
        }
        
        function showOrderProcessing(message = getTranslation('processing_please_wait')) {
            const orderResultContainer = document.getElementById('order-result-container');
            if (orderResultContainer) {
                orderResultContainer.innerHTML = `
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p class="loading-text">${message}</p>
                    </div>
                `;
            }
            
            // 朗讀正在處理的消息
            speakText(getTranslation('ai_analyzing_order'));
            
            hideOrderMessages();
        }

        // 增強的 AI 處理視覺化函數
        function showEnhancedOrderProcessing(message = getTranslation('processing_please_wait')) {
            const orderResultContainer = document.getElementById('order-result-container');
            if (orderResultContainer) {
                orderResultContainer.innerHTML = `
                    <div class="ai-processing-container">
                        <!-- AI 處理標題 -->
                        <div class="ai-header">
                            <div class="ai-icon">🤖</div>
                            <h3>${getTranslation('ai_processing_title')}</h3>
                        </div>
                        
                        <!-- 處理步驟 -->
                        <div class="processing-steps">
                            <div class="step active" id="step-1">
                                <div class="step-icon">📝</div>
                                <div class="step-content">
                                    <span class="step-title">${getTranslation('step_receive_order')}</span>
                                    <div class="step-progress"></div>
                                </div>
                            </div>
                            <div class="step" id="step-2">
                                <div class="step-icon">🧠</div>
                                <div class="step-content">
                                    <span class="step-title">${getTranslation('step_ai_analysis')}</span>
                                    <div class="step-progress"></div>
                                </div>
                            </div>
                            <div class="step" id="step-3">
                                <div class="step-icon">🍽️</div>
                                <div class="step-content">
                                    <span class="step-title">${getTranslation('step_menu_matching')}</span>
                                    <div class="step-progress"></div>
                                </div>
                            </div>
                            <div class="step" id="step-4">
                                <div class="step-icon">✨</div>
                                <div class="step-content">
                                    <span class="step-title">${getTranslation('step_generate_result')}</span>
                                    <div class="step-progress"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 主要訊息 -->
                        <div class="main-message">
                            <div class="loading-spinner"></div>
                            <p class="loading-text">${message}</p>
                        </div>
                        
                        <!-- AI 思考氣泡 -->
                        <div class="ai-thinking">
                            <div class="thinking-bubble">
                                <span class="thinking-text">${getTranslation('ai_thinking_text')}</span>
                                <div class="thinking-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 啟動處理步驟動畫
                startProcessingAnimation();
            }
            
            // 朗讀正在處理的消息
            speakText(getTranslation('gemini_ai_analyzing_order'));
            
            hideOrderMessages();
        }
        
        // AI 處理動畫函數
        function startProcessingAnimation() {
            const steps = ['step-1', 'step-2', 'step-3', 'step-4'];
            const thinkingTexts = [
                getTranslation('ai_thinking_text'),
                getTranslation('step_ai_analysis') + '...',
                getTranslation('step_menu_matching') + '...',
                getTranslation('preparing_results')
            ];
            
            let currentStep = 0;
            
            function animateNextStep() {
                if (currentStep > 0) {
                    // 完成前一步
                    const prevStep = document.getElementById(steps[currentStep - 1]);
                    if (prevStep) {
                        prevStep.classList.remove('active');
                        prevStep.classList.add('completed');
                    }
                }
                
                if (currentStep < steps.length) {
                    // 激活當前步驟
                    const currentStepElement = document.getElementById(steps[currentStep]);
                    if (currentStepElement) {
                        currentStepElement.classList.add('active');
                    }
                    
                    // 更新思考文字
                    const thinkingText = document.querySelector('.thinking-text');
                    if (thinkingText && thinkingTexts[currentStep]) {
                        thinkingText.textContent = thinkingTexts[currentStep];
                    }
                    
                    currentStep++;
                    setTimeout(animateNextStep, 1500); // 每1.5秒進行下一步
                }
            }            setTimeout(animateNextStep, 500); // 0.5秒後開始動畫
        }
        
        // 隱藏訂單消息函數
        function hideOrderMessages() {
            // 隱藏成功和錯誤消息
            const successMsg = document.getElementById('order-success');
            const errorMsg = document.getElementById('order-error');
            
            if (successMsg) successMsg.style.display = 'none';
            if (errorMsg) errorMsg.style.display = 'none';
        }

        // 顯示訂單成功訊息
        function showOrderSuccess(message) {
            const orderSuccess = document.getElementById('order-success');
            if (orderSuccess) {
                orderSuccess.innerHTML = message;
                orderSuccess.style.display = 'block';
            }
            
            // 朗讀成功消息
            speakText(message);
        }
        
        // 顯示訂單錯誤訊息
        function showOrderError(message) {
            const orderError = document.getElementById('order-error');
            if (orderError) {
                orderError.innerHTML = message;
                orderError.style.display = 'block';
            }            // 朗讀錯誤消息
            speakText(message);
        }
        
        // 顯示 USI AIOS 的自然語言回應結果
        function showGeminiOrderResult(geminiResponse, parameters) {
            const orderResultContainer = document.getElementById('order-result-container');
            
            if (!orderResultContainer) {
                console.error('找不到訂單結果容器');
                return;
            }
            
            console.log(getTranslation('displaying_gemini_response') + ':', geminiResponse);
            console.log('參數:', parameters);            // 保存當前的 Gemini 訂單數據供確認時使用
            window.currentGeminiOrderData = {
                response: geminiResponse,
                parameters: parameters,
                timestamp: new Date().toISOString()
            };
            
            console.log(getTranslation('set_gemini_order_data') + ':', window.currentGeminiOrderData);
            
            // 創建 USI AIOS 回應的顯示界面
            orderResultContainer.innerHTML = `
                <div class="gemini-response-container">
                    <div class="gemini-header">
                        <div class="gemini-icon">🤖</div>
                        <h3 id="gemini-assistant-title" data-i18n="gemini_assistant_title">${getTranslation('gemini_assistant_title')}</h3>
                    </div>
                    <div class="gemini-response">
                        <div class="response-content">
                            ${formatGeminiResponse(geminiResponse)}
                        </div>
                    </div>
                    <div class="gemini-actions">
                        <button onclick="confirmGeminiOrder()" class="btn btn-primary" data-i18n="confirm_order_btn">
                            <i class="fas fa-check"></i> ${getTranslation('confirm_order_btn')}
                        </button>
                        <button onclick="modifyOrder()" class="btn btn-secondary" data-i18n="modify_order_btn">
                            <i class="fas fa-edit"></i> ${getTranslation('modify_order_btn')}
                        </button>
                        <button onclick="clearOrder()" class="btn btn-outline" data-i18n="restart_order_btn">
                            <i class="fas fa-times"></i> ${getTranslation('restart_order_btn')}
                        </button>
                    </div>
                </div>
            `;
            
            // 添加 Gemini 回應的樣式
            addGeminiResponseStyles();

            // 立即檢查並設置按鈕狀態
            const dynamicConfirmBtn = document.getElementById('dynamic-confirm-btn');
            if (dynamicConfirmBtn) {
                const isValid = isOrderValid();
                console.log(`動態按鈕創建後檢查訂單狀態: ${isValid ? '有效' : '無效'}`);

                if (!isValid) {
                    dynamicConfirmBtn.disabled = true;
                    dynamicConfirmBtn.style.opacity = '0.5';
                    dynamicConfirmBtn.style.cursor = 'not-allowed';
                    dynamicConfirmBtn.title = getTranslation('order_empty_cannot_confirm') || '訂單為空，無法確認';
                    console.log('動態按鈕已禁用');
                } else {
                    dynamicConfirmBtn.disabled = false;
                    dynamicConfirmBtn.style.opacity = '1';
                    dynamicConfirmBtn.style.cursor = 'pointer';
                    dynamicConfirmBtn.title = '';
                    console.log('動態按鈕已啟用');
                }
            }

            // 延遲更新按鈕狀態，確保 DOM 更新完成
            setTimeout(() => {
                if (typeof updateConfirmOrderButtonState === 'function') {
                    updateConfirmOrderButtonState();
                }
            }, 100);

            // 再次延遲更新，確保所有元素都已渲染
            setTimeout(() => {
                if (typeof updateConfirmOrderButtonState === 'function') {
                    updateConfirmOrderButtonState();
                }
            }, 500);

            // 朗讀 Gemini 回應 (簡化版本，移除圖片URL)
            try {
                let simplifiedResponse = geminiResponse
                    .replace(/\[圖片:\s*https?:\/\/[^\]]+\]/g, '') // 移除圖片URL標記
                    .replace(/ORDER_JSON_START[\s\S]*?ORDER_JSON_END/g, '') // 移除JSON部分
                    .replace(/<[^>]*>/g, '') // 移除HTML標籤
                    .replace(/\n+/g, ' ') // 將換行符替換為空格
                    .trim();

                // 確保有內容可以播放
                if (simplifiedResponse.length > 0) {
                    // 限制長度以避免過長的語音
                    if (simplifiedResponse.length > 150) {
                        simplifiedResponse = simplifiedResponse.substring(0, 150) + '...';
                    }

                    const speechText = getTranslation('ai_analyzed_order') + ' ' + simplifiedResponse;
                    console.log('🔊 準備播放語音:', speechText);

                    // 延遲播放，確保 DOM 更新完成
                    setTimeout(() => {
                        speakText(speechText, true); // 強制播放
                    }, 500);
                } else {
                    // 如果沒有有效內容，播放預設訊息
                    console.log('⚠️ 沒有有效的語音內容，播放預設訊息');
                    setTimeout(() => {
                        speakText(getTranslation('order_analysis_complete'), true);
                    }, 500);
                }
            } catch (error) {
                console.error('❌ 語音播放準備失敗:', error);
                // 播放備用訊息
                setTimeout(() => {
                    speakText(getTranslation('order_analysis_complete'), true);
                }, 500);
            }
        }        // 格式化 Gemini 回應文本
        function formatGeminiResponse(response) {
            console.log('🎨 格式化 Gemini 回應:', response);

            // 分離自然語言部分和 JSON 部分
            const orderJsonStart = response.indexOf('ORDER_JSON_START');
            const orderJsonEnd = response.indexOf('ORDER_JSON_END');

            let naturalLanguagePart = response;
            let jsonPart = '';

            if (orderJsonStart !== -1 && orderJsonEnd !== -1) {
                // 提取自然語言部分（ORDER_JSON_START 之前的內容）
                naturalLanguagePart = response.substring(0, orderJsonStart).trim();

                // 提取 JSON 部分
                const jsonStartPos = orderJsonStart + 'ORDER_JSON_START'.length;
                const jsonContent = response.substring(jsonStartPos, orderJsonEnd).trim();

                console.log('📝 自然語言部分:', naturalLanguagePart);
                console.log('📋 JSON 部分:', jsonContent);

                try {
                    const orderData = JSON.parse(jsonContent);
                    console.log('✅ 成功解析 JSON:', orderData);

                    // 生成簡潔的訂單摘要
                    if (orderData.items && Array.isArray(orderData.items)) {
                        jsonPart = `<div class="order-summary-section"><h4>📋 ${getTranslation('order_summary')}</h4>`;

                        // 根據當前語言設定決定貨幣符號
                        function getCurrencySymbolByLanguage() {
                            const currentLanguage = getCurrentLanguage();
                            switch (currentLanguage) {
                                case 'ja-JP':
                                    return '¥';
                                case 'en-US':
                                    return 'NT$';  // 英文介面也使用 NT$ 避免與美元混淆
                                case 'zh-TW':
                                default:
                                    return 'NT$';
                            }
                        }

                        let currencySymbol = getCurrencySymbolByLanguage();

                        console.log('🔍 檢測到的貨幣符號:', currencySymbol);
                        console.log('🔍 自然語言部分:', naturalLanguagePart);

                        orderData.items.forEach(item => {
                            const itemTotal = item.price * item.quantity;

                            jsonPart += `<div class="order-item-simple">`;

                            // 顯示餐點圖片（如果有）
                            if (item.image_url) {
                                jsonPart += `<div class="item-image">
                                    <img src="${item.image_url}" alt="${item.name}"
                                         style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover;"
                                         onerror="this.style.display='none';" onload="this.style.display='block';">
                                </div>`;
                            }

                            // 顯示餐點信息
                            jsonPart += `<div class="item-info">
                                <div class="item-name">${item.name} <span class="unit-price">(${getTranslation('unit_price')}: ${currencySymbol}${item.price})</span></div>
                                <div class="item-quantity">${getTranslation('quantity')}: ${item.quantity}</div>
                                <div class="item-total">${currencySymbol}${itemTotal}</div>
                            </div>`;

                            jsonPart += `</div>`;
                        });

                        // 顯示總計
                        if (orderData.total) {
                            jsonPart += `<div class="total-section">
                                <strong>${getTranslation('total')}: ${currencySymbol}${orderData.total}</strong>
                            </div>`;
                        }

                        jsonPart += '</div>';
                    }
                } catch (error) {
                    console.error('❌ JSON 解析失敗:', error);
                    jsonPart = '<div class="json-error">訂單格式解析失敗</div>';
                }
            }

            // 格式化自然語言部分
            let formatted = naturalLanguagePart.replace(/\n/g, '<br>');

            // 突出顯示價格信息
            formatted = formatted.replace(/[¥￥](\d+)/g, function(match, price) {
                return '<span class="price-highlight">¥' + price + '</span>';
            });
            formatted = formatted.replace(/NT\$(\d+)/g, function(match, price) {
                return '<span class="price-highlight">NT$' + price + '</span>';
            });

            // 突出顯示數量信息
            formatted = formatted.replace(/(\d+)\s*[×x]\s*/g, '<span class="quantity-highlight">$1×</span>');

            // 移除自然語言部分的圖片標記（因為圖片會在 JSON 部分顯示）
            formatted = formatted.replace(/\[圖片:\s*(https?:\/\/[^\]]+)\]/g, '');

            // 組合結果
            return formatted + (jsonPart ? '<br><br>' + jsonPart : '');
        }
        
        // 添加 Gemini 回應的 CSS 樣式
        function addGeminiResponseStyles() {
            const existingStyle = document.getElementById('gemini-response-styles');
            if (!existingStyle) {
                const styleElement = document.createElement('style');
                styleElement.id = 'gemini-response-styles';
                styleElement.textContent = `
                .gemini-response-container {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 15px;
                    padding: 25px;
                    color: white;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    margin: 20px 0;
                }
                
                .gemini-header {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    margin-bottom: 20px;
                    border-bottom: 1px solid rgba(255,255,255,0.2);
                    padding-bottom: 15px;
                }
                
                .gemini-icon {
                    font-size: 2em;
                }
                
                .gemini-header h3 {
                    margin: 0;
                    font-size: 1.4em;
                }
                
                .gemini-response {
                    background: rgba(255,255,255,0.1);
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                }
                
                .response-content {
                    line-height: 1.6;
                    font-size: 1.1em;
                }
                
                .price-highlight {
                    background: rgba(255,215,0,0.3);
                    color: #FFD700;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                
                .quantity-highlight {
                    background: rgba(76,175,80,0.3);
                    color: #4CAF50;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: bold;
                }

                .order-summary-section {
                    margin-top: 20px;
                    padding: 15px;
                    background: white;
                    border-radius: 10px;
                    border: 1px solid rgba(255,255,255,0.3);
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }

                .order-summary-section h4 {
                    margin: 0 0 15px 0;
                    color: #333;
                    font-size: 1.2em;
                    font-weight: bold;
                }

                .order-item-simple {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                    padding: 12px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    border-left: 3px solid #007bff;
                    gap: 15px;
                }

                .item-image {
                    flex-shrink: 0;
                }

                .item-info {
                    flex-grow: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .item-name {
                    font-weight: bold;
                    color: #333;
                    font-size: 1.1em;
                }

                .unit-price {
                    font-weight: normal;
                    color: #666;
                    font-size: 0.9em;
                    margin-left: 8px;
                }

                .item-quantity {
                    color: #666;
                    font-size: 0.95em;
                }

                .item-total {
                    color: #007bff;
                    font-weight: bold;
                    font-size: 1.1em;
                }

                .total-section {
                    margin-top: 15px;
                    padding: 10px;
                    background: #e3f2fd;
                    border-radius: 8px;
                    text-align: center;
                    font-size: 1.2em;
                    color: #333;
                    border: 1px solid #007bff;
                }

                .json-error {
                    color: #FF6B6B;
                    font-style: italic;
                    padding: 10px;
                    background: rgba(255,107,107,0.1);
                    border-radius: 5px;
                }
                
                .gemini-actions {
                    display: flex;
                    gap: 15px;
                    margin-top: 20px;
                    flex-wrap: wrap;
                }
                
                .gemini-actions .btn {
                    flex: 1;
                    min-width: 120px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-weight: bold;
                    transition: all 0.3s;
                }
                
                .gemini-actions .btn-primary {
                    background: #4CAF50;
                    border: none;
                    color: white;
                }
                
                .gemini-actions .btn-secondary {
                    background: #2196F3;
                    border: none;
                    color: white;
                }
                
                .gemini-actions .btn-outline {
                    background: transparent;
                    border: 2px solid rgba(255,255,255,0.5);
                    color: white;
                }
                
                .gemini-actions .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                }
                
                @media (max-width: 768px) {
                    .gemini-actions {
                        flex-direction: column;
                    }
                    
                    .gemini-actions .btn {
                        min-width: 100%;
                    }
                }
                `;
                document.head.appendChild(styleElement);
            }
        }        // 確認 USI AIOS 訂單
        // 檢查訂單是否有效
        function isOrderValid() {
            console.log('開始檢查訂單有效性...');

            // 檢查方法1：傳統表格結構
            const tableItems = document.querySelectorAll('.preview-table tbody tr');
            let hasValidTableItems = false;

            if (tableItems && tableItems.length > 0) {
                console.log(`找到 ${tableItems.length} 個表格項目`);
                tableItems.forEach((item, index) => {
                    const nameCell = item.querySelector('.item-name');
                    const totalCell = item.querySelector('.item-total');

                    if (nameCell && totalCell) {
                        const name = nameCell.textContent.trim();
                        const totalText = totalCell.textContent.replace(/NT\$|¥/g, '').trim();
                        const itemTotal = parseFloat(totalText) || 0;

                        console.log(`表格項目 ${index}: 名稱="${name}", 價格="${totalText}", 解析價格=${itemTotal}`);

                        if (name && name !== '未知餐點' && !isNaN(itemTotal) && itemTotal > 0) {
                            hasValidTableItems = true;
                            console.log(`表格項目 ${index} 有效`);
                        }
                    }
                });
            } else {
                console.log('沒有找到表格項目');
            }

            // 檢查方法2：Gemini 回應中的訂單結構
            const geminiItems = document.querySelectorAll('.order-item-simple');
            let hasValidGeminiItems = false;

            if (geminiItems && geminiItems.length > 0) {
                console.log(`找到 ${geminiItems.length} 個 Gemini 項目`);
                geminiItems.forEach((item, index) => {
                    const nameElement = item.querySelector('.item-name');
                    const totalElement = item.querySelector('.item-total');

                    if (nameElement && totalElement) {
                        const name = nameElement.textContent.trim();
                        const totalText = totalElement.textContent.replace(/NT\$|¥/g, '').trim();
                        const itemTotal = parseFloat(totalText) || 0;

                        console.log(`Gemini 項目 ${index}: 名稱="${name}", 價格="${totalText}", 解析價格=${itemTotal}`);

                        if (name && name !== '未知餐點' && !isNaN(itemTotal) && itemTotal > 0) {
                            hasValidGeminiItems = true;
                            console.log(`Gemini 項目 ${index} 有效`);
                        }
                    }
                });
            } else {
                console.log('沒有找到 Gemini 項目');
            }

            // 檢查方法3：檢查 AI 回應內容（更嚴格的檢查）
            let hasValidAIResponse = false;
            const responseContent = document.querySelector('.response-content');
            if (responseContent) {
                const responseText = responseContent.textContent || responseContent.innerText || '';
                console.log('AI 回應內容長度:', responseText.length);

                // 更嚴格的檢查：必須同時包含價格信息、餐點信息，且不能是詢問或錯誤訊息
                const hasPriceInfo = responseText.includes('NT$') || responseText.includes('¥');
                const hasMenuItems = responseText.includes('餐') || responseText.includes('套餐') || responseText.includes('飲料') ||
                                   responseText.includes('meal') || responseText.includes('drink') || responseText.includes('food');

                // 檢查是否包含訂單確認的關鍵字
                const hasOrderConfirmation = responseText.includes('總共') || responseText.includes('總計') || responseText.includes('總金額') ||
                                           responseText.includes('total') || responseText.includes('Total') ||
                                           responseText.includes('您的訂單') || responseText.includes('您點了') ||
                                           responseText.includes('為您準備') || responseText.includes('已為您');

                // 檢查是否是詢問或錯誤訊息（這些情況下不應該啟用確認按鈕）
                const isQuestion = responseText.includes('請告訴我') || responseText.includes('您想要') || responseText.includes('請問') ||
                                 responseText.includes('what would you like') || responseText.includes('please tell me') ||
                                 responseText.includes('沒有找到') || responseText.includes('無法找到') || responseText.includes('not found') ||
                                 responseText.includes('請重新') || responseText.includes('請再次') || responseText.includes('please try again');

                if (hasPriceInfo && hasMenuItems && hasOrderConfirmation && !isQuestion) {
                    hasValidAIResponse = true;
                    console.log('AI 回應包含有效的訂單信息');
                } else {
                    console.log(`AI 回應檢查: 價格信息=${hasPriceInfo}, 餐點信息=${hasMenuItems}, 訂單確認=${hasOrderConfirmation}, 是詢問=${isQuestion}`);
                }
            } else {
                console.log('沒有找到 AI 回應內容');
            }

            // 檢查方法4：檢查是否有 currentGeminiOrderData（更嚴格的檢查）
            let hasGeminiOrderData = false;
            if (window.currentGeminiOrderData && window.currentGeminiOrderData.response) {
                const response = window.currentGeminiOrderData.response;
                console.log('currentGeminiOrderData 存在，回應長度:', response.length);

                // 更嚴格的檢查：必須包含價格信息和訂單確認，且不能是詢問
                const hasPriceInfo = response.includes('NT$') || response.includes('¥');
                const hasOrderConfirmation = response.includes('總共') || response.includes('總計') || response.includes('總金額') ||
                                           response.includes('total') || response.includes('Total') ||
                                           response.includes('您的訂單') || response.includes('您點了') ||
                                           response.includes('為您準備') || response.includes('已為您');

                const isQuestion = response.includes('請告訴我') || response.includes('您想要') || response.includes('請問') ||
                                 response.includes('what would you like') || response.includes('please tell me') ||
                                 response.includes('沒有找到') || response.includes('無法找到') || response.includes('not found');

                if (hasPriceInfo && hasOrderConfirmation && !isQuestion) {
                    hasGeminiOrderData = true;
                    console.log('currentGeminiOrderData 包含有效的訂單信息');
                } else {
                    console.log(`currentGeminiOrderData 檢查: 價格信息=${hasPriceInfo}, 訂單確認=${hasOrderConfirmation}, 是詢問=${isQuestion}`);
                }
            } else {
                console.log('沒有 currentGeminiOrderData');
            }

            const isValid = hasValidTableItems || hasValidGeminiItems || hasValidAIResponse || hasGeminiOrderData;
            console.log(`訂單驗證結果: 表格項目=${hasValidTableItems}, Gemini項目=${hasValidGeminiItems}, AI回應=${hasValidAIResponse}, Gemini數據=${hasGeminiOrderData}, 總體有效=${isValid}`);

            return isValid;
        }

        // 更新確認訂單按鈕狀態
        function updateConfirmOrderButtonState() {
            // 使用更廣泛的選擇器來找到確認訂單按鈕
            const confirmButtons = document.querySelectorAll(
                'button[onclick="confirmGeminiOrder()"], ' +
                '.confirm-btn, ' +
                'button.confirm-btn, ' +
                'button:contains("確認訂單"), ' +
                'button:contains("Confirm Order"), ' +
                'button:contains("注文確認")'
            );

            // 額外檢查：通過文本內容查找按鈕
            const allButtons = document.querySelectorAll('button');
            const additionalButtons = Array.from(allButtons).filter(button => {
                const text = button.textContent || button.innerText || '';
                return text.includes('確認訂單') ||
                       text.includes('Confirm Order') ||
                       text.includes('注文確認') ||
                       button.classList.contains('confirm-btn') ||
                       button.getAttribute('onclick') === 'confirmGeminiOrder()';
            });

            // 合併所有找到的按鈕
            const allConfirmButtons = new Set([...confirmButtons, ...additionalButtons]);
            const isValid = isOrderValid();

            console.log(`找到 ${allConfirmButtons.size} 個確認訂單按鈕`);

            allConfirmButtons.forEach(button => {
                if (isValid) {
                    button.disabled = false;
                    button.style.opacity = '1';
                    button.style.cursor = 'pointer';
                    button.title = '';
                } else {
                    button.disabled = true;
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                    button.title = getTranslation('order_empty_cannot_confirm') || '訂單為空，無法確認';
                }
                console.log(`按鈕文本: "${button.textContent}", 狀態: ${isValid ? '啟用' : '禁用'}`);
            });

            console.log(`確認訂單按鈕狀態更新: ${isValid ? '啟用' : '禁用'}`);
        }

        function confirmGeminiOrder() {
            // 首先檢查訂單是否有效
            if (!isOrderValid()) {
                console.log('訂單無效，無法確認');
                showToastMessage(getTranslation('order_empty_cannot_confirm') || '訂單為空，無法確認', 'warning');
                return;
            }

            // 獲取當前的 USI AIOS 回應
            const aiResponse = window.currentGeminiOrderData ? window.currentGeminiOrderData.response : null;

            // 使用修復版的結帳功能處理 USI AIOS 回應中的訂單資訊
            if (typeof window.proceedToCheckout === 'function') {
                    console.log(getTranslation('using_fixed_checkout'));
                    window.proceedToCheckout();
                } else {
                    console.error(getTranslation('fixed_checkout_not_found'));
                showOrderConfirmationDialog(aiResponse);
            }
        }
        // 全局變量來存儲當前的 Gemini 訂單數據
        window.currentGeminiOrderData = window.currentGeminiOrderData || null;
        
        // 修改訂單
        function modifyOrder() {
            recordUserInteraction();
            const orderInput = document.getElementById('order-input');
            if (orderInput) {
                orderInput.focus();
                showToastMessage(getTranslation('enter_modification_request'), 'info');
            }
        }
        
        // 清空訂單
        function clearOrder() {
            recordUserInteraction();
            const orderInput = document.getElementById('order-input');
            const orderResultContainer = document.getElementById('order-result-container');
            
            if (orderInput) {
                orderInput.value = '';
            }
            
            if (orderResultContainer) {
                orderResultContainer.innerHTML = '';
            }
              showToastMessage(getTranslation('order_cleared_success'), 'success');
        }
        
        // 訂單確認對話框函數
        function showOrderConfirmationDialog(aiResponse) {
            console.log(getTranslation('showing_order_confirmation') + '，AI 回應:', aiResponse);

            // 根據當前語言設定決定貨幣符號
            function getCurrencySymbolByLanguage() {
                const currentLanguage = getCurrentLanguage();
                switch (currentLanguage) {
                    case 'ja-JP':
                        return '¥';
                    case 'en-US':
                        return '$';
                    case 'zh-TW':
                    default:
                        return 'NT$';
                }
            }

            const modalOrderSummary = document.getElementById('modal-order-summary');
            const modalTotalAmount = document.getElementById('modal-total-amount');
            
            if (!modalOrderSummary || !modalTotalAmount) {
                console.error(getTranslation('order_confirmation_modal_not_found'));
                alert(getTranslation('order_confirmation_unavailable'));
                return;
            }
            
            // 顯示 USI AIOS 回應中的餐點名稱和價格
            let summaryHtml = '';
            let totalAmount = 0;
            
            if (aiResponse) {
                // 提取包含餐點名稱和價格的行
                const lines = aiResponse.split('\n');
                const menuItems = [];
                
                for (let line of lines) {
                    // 查找包含價格的行（支援 NT$, ¥ 等貨幣符號）
                    const priceMatch = line.match(/(.+?)\s*(?:NT\$|¥)(\d+)/);
                    if (priceMatch) {
                        const itemText = priceMatch[1].replace(/[•\-\*]/g, '').trim();
                        const price = parseInt(priceMatch[2]);
                        if (itemText && price > 0) {
                            // 根據當前語言設定決定貨幣符號
                            const currency = getCurrencySymbolByLanguage();
                            menuItems.push(`${itemText} ${currency}${price}`);
                            totalAmount += price;
                        }
                    }
                }
                
                // 生成簡潔的摘要 HTML
                if (menuItems.length > 0) {
                    summaryHtml = '<div style="text-align: left; line-height: 1.6;">';
                    menuItems.forEach(item => {
                        summaryHtml += `<div style="margin-bottom: 5px;">${item}</div>`;
                    });
                    summaryHtml += '</div>';
                } else {
                    // 如果無法解析，顯示簡化的原始回應
                    const simplifiedResponse = aiResponse
                        .split('\n')
                        .filter(line => line.includes('NT$') || line.includes('¥'))
                        .join('<br>');
                    summaryHtml = `<div style="text-align: left;">${simplifiedResponse || aiResponse}</div>`;
                    
                    // 嘗試提取總價
                    const totalMatch = aiResponse.match(/總共[是]*\s*(?:NT\$|¥)(\d+)/i);
                    if (totalMatch) {
                        totalAmount = parseInt(totalMatch[1]);
                    }
                }
            } else {
                summaryHtml = `<p>${getTranslation('unable_to_get_order_info')}</p>`;
            }
            
            // 更新模態框內容
            modalOrderSummary.innerHTML = summaryHtml;
            modalTotalAmount.textContent = totalAmount > 0 ? `${getCurrencySymbolByLanguage()}${totalAmount}` : getTranslation('please_confirm_price');
            
            // 顯示模態框
            const modal = document.getElementById('order-confirmation-modal');
            if (modal) {
                modal.style.display = 'flex';
                console.log(getTranslation('order_confirmation_modal_shown'));
            } else {
                console.error(getTranslation('order_confirmation_modal_not_found'));
                alert(getTranslation('order_confirmation_unavailable'));
            }
        }

        // Toast 通知函數
        function showToastMessage(message, type = 'success') {
            console.log(`${getTranslation('showing_toast_notification')}: [${type}] ${message}`);
            
            // 獲取或創建 Toast 容器
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = createToastContainer();
            }
            
            // 創建 Toast 元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = message;  // 使用 innerHTML 支援 HTML 標記
            
            // 添加到容器
            toastContainer.appendChild(toast);
            
            // 自動移除 Toast
            setTimeout(() => {
                toast.classList.add('toast-fade-out');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 500);
            }, 3000);
        }

        // 創建 Toast 容器函數
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.style.position = 'fixed';
            container.style.bottom = '20px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            console.log('✅ ' + getTranslation('toast_container_created'));
            return container;
        }
    </script>

    <!-- 🎯 覆蓋 showOrderResult 函數，直接顯示 USI AIOS 回應 -->
    <script>
        // 等待頁面完全載入後覆蓋函數
        document.addEventListener('DOMContentLoaded', function() {
            // 覆蓋原始的 showOrderResult 函數
            window.showOrderResult = function(items, analysis) {
                const orderResultContainer = document.getElementById('order-result-container');
                
                if (!orderResultContainer) {
                    console.error(getTranslation('order_result_container_not_found'));
                    return;
                }
                
                console.log('🎯 使用簡化版 showOrderResult - USI AIOS 回傳的原始內容:', items);
                console.log('🎯 分析數據:', analysis);
                
                // 檢查是否有 AI 自然語言回應
                let aiResponse = null;
                if (analysis && analysis.aiResponse) {
                    aiResponse = analysis.aiResponse;
                } else if (items && items.data && items.data.aiResponse) {
                    aiResponse = items.data.aiResponse;
                }
                
                if (aiResponse) {
                    // 🎯 直接顯示 USI AIOS 的自然語言回應，不再解析
                    orderResultContainer.innerHTML = `
                        <div class="ai-response-container">
                            <h3>🤖 ${getTranslation('gemini_ai_response')}</h3>
                            <div class="response-content" style="
                                background: #f5f5f5;
                                padding: 20px;
                                border-radius: 8px;
                                margin: 10px 0;
                                font-size: 16px;
                                line-height: 1.6;
                                border-left: 4px solid #4CAF50;
                            ">
                                ${aiResponse}
                            </div>
                            <div class="action-buttons" style="
                                margin-top: 20px;
                                display: flex;
                                gap: 10px;
                                justify-content: center;
                            ">
                                <button class="btn confirm-btn" onclick="confirmGeminiOrder()" id="dynamic-confirm-btn" style="
                                    background: #4CAF50;
                                    color: white;
                                    padding: 12px 24px;
                                    border: none;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 16px;
                                ">${getTranslation('confirm_order')}</button>
                                <button class="btn modify-btn" onclick="modifyOrder()" style="
                                    background: #2196F3;
                                    color: white;
                                    padding: 12px 24px;
                                    border: none;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 16px;
                                ">${getTranslation('modify_order')}</button>
                                <button class="btn clear-btn" onclick="clearOrder()" style="
                                    background: #f44336;
                                    color: white;
                                    padding: 12px 24px;
                                    border: none;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-size: 16px;
                                ">${getTranslation('clear_order')}</button>
                            </div>
                        </div>
                    `;
                    return;
                }
                
                // 如果沒有 AI 回應，顯示錯誤信息
                console.error('沒有找到 AI 回應內容');
                orderResultContainer.innerHTML = `
                    <div class="no-matches">
                        <h3>無法獲取 AI 回應</h3>
                        <p>請重新嘗試您的點餐請求</p>
                    </div>
                `;
            };            // 覆蓋確認 USI AIOS 訂單函數
            window.confirmGeminiOrder = function() {
                console.log('🎯 確認 USI AIOS 訂單');

                // 首先檢查訂單是否有效
                if (typeof window.isOrderValid === 'function' && !window.isOrderValid()) {
                    console.log('訂單無效，無法確認');
                    if (typeof showToastMessage === 'function') {
                        showToastMessage(getTranslation('order_empty_cannot_confirm') || '訂單為空，無法確認', 'warning');
                    } else {
                        alert(getTranslation('order_empty_cannot_confirm') || '訂單為空，無法確認');
                    }
                    return;
                }

                // 優先使用修復版的結帳功能處理 USI AIOS 回應中的訂單資訊
                if (typeof window.proceedToCheckout === 'function') {
                    console.log('使用修復版結帳功能處理 USI AIOS 回應');
                    window.proceedToCheckout();
                } else {
                    console.log('找不到修復版結帳功能，使用備用確認對話框');

                    // 從 AI 回應中獲取內容
                    const responseContent = document.querySelector('.response-content');
                    if (responseContent) {
                        const aiResponse = responseContent.textContent || responseContent.innerText || '';
                        console.log('AI 回應內容:', aiResponse);

                        // 檢查是否有訂單確認對話框函數
                        if (typeof showOrderConfirmationDialog === 'function') {
                            showOrderConfirmationDialog(aiResponse);
                        } else {
                            alert(getTranslation('order_confirmation_unavailable'));
                        }
                    } else {
                        alert(getTranslation('no_order_content'));
                    }
                }
            };
            
            /* 
            // 簡化版訂單確認對話框 - 已註解，因為有 JavaScript 錯誤
            window.showGeminiOrderConfirmationDialog = function(aiResponse) {
                // 整個函數已被註解掉
            };
            */
            
            console.log('🎯 已覆蓋 showOrderResult 函數 - 現在將直接顯示 Gemini AI 回應');
        });    </script>
    <!-- 移除強制重新載入頁面的腳本，在生產環境中不需要 -->
      <!-- 移除訂單摘要顯示調試工具 -->
    
    <!-- 簡化確認訂單按鈕的處理程序 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('初始化確認訂單按鈕事件處理程序');

            // 初始化按鈕狀態
            if (typeof updateConfirmOrderButtonState === 'function') {
                setTimeout(() => {
                    updateConfirmOrderButtonState();
                }, 500);
            }

            // 找到確認訂單按鈕並設置正確的點擊事件
            const confirmButtons = document.querySelectorAll('button[onclick="confirmGeminiOrder()"]');
            confirmButtons.forEach(button => {
                console.log('找到確認訂單按鈕');
                button.onclick = function(e) {
                    e.preventDefault();
                    console.log('確認訂單');
                    window.confirmGeminiOrder();
                };
            });

            // 設置定期檢查按鈕狀態
            setInterval(() => {
                if (typeof updateConfirmOrderButtonState === 'function') {
                    updateConfirmOrderButtonState();
                }
            }, 2000); // 每2秒檢查一次
        });
    </script>
</body>
</html>